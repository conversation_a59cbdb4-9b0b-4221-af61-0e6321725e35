---
title: 'Topological Sorting - Ordering Vertices in Directed Acyclic Graphs'
date: '2025-05-04'
tags: ['DSA']
draft: false
summary: Comprehensive guide to topological sorting algorithms including DFS-based and Kahn algorithm approaches with applications in scheduling, dependency resolution, and build systems.
---

Topological sorting was initially one of those graph algorithms that seemed abstract to me during DSA preparation. I kept thinking, "Why do we need to order vertices?" But once I understood real-world applications like course prerequisites, build dependencies, and task scheduling, the importance became crystal clear. Let me share what I've learned about this fundamental graph algorithm.

## **Introduction to Topological Sorting**

When I first encountered topological sorting, I was confused about why we needed special ordering for graph vertices. But then I realized it's everywhere - from university course planning to software compilation - whenever we need to respect dependencies and prerequisites.

**Topological sorting** is a linear ordering of vertices in a directed acyclic graph (DAG) such that for every directed edge (u, v), vertex u comes before vertex v in the ordering.

## **Prerequisites and Definitions**

### **Directed Acyclic Graph (DAG)**

**Requirements for topological sorting:**
- Graph must be **directed** (edges have direction)
- Graph must be **acyclic** (no cycles)
- If cycles exist, topological ordering is impossible

**Why no cycles?**
If there's a cycle A → B → C → A, then:
- A must come before B
- B must come before C  
- C must come before A
- This creates a contradiction!

### **Example DAG**

```
Course Prerequisites:
Math101 → Math201 → Math301
   ↓         ↓
Physics101 → Physics201
   ↓
Chemistry101

Possible topological orders:
1. Math101, Physics101, Chemistry101, Math201, Physics201, Math301
2. Math101, Physics101, Math201, Chemistry101, Physics201, Math301
3. Physics101, Math101, Chemistry101, Math201, Physics201, Math301
```

I remember drawing course dependency graphs to understand this concept!

## **DFS-Based Topological Sorting**

### **Algorithm Overview**

**Key insight:** In DFS, vertices are finished in reverse topological order. If we record finish times and sort by decreasing finish time, we get topological order.

**Algorithm:**
1. Perform DFS on the entire graph
2. When a vertex finishes (all descendants processed), add it to result
3. Reverse the result to get topological order

### **Implementation**

```python
from collections import defaultdict

class Graph:
    def __init__(self, vertices):
        self.V = vertices
        self.graph = defaultdict(list)
    
    def add_edge(self, u, v):
        """Add directed edge from u to v"""
        self.graph[u].append(v)
    
    def topological_sort_dfs(self):
        """Topological sort using DFS"""
        visited = [False] * self.V
        stack = []
        
        # Call DFS for all unvisited vertices
        for i in range(self.V):
            if not visited[i]:
                self._dfs_topological(i, visited, stack)
        
        # Return vertices in topological order
        return stack[::-1]  # Reverse the stack
    
    def _dfs_topological(self, v, visited, stack):
        """DFS helper for topological sorting"""
        visited[v] = True
        
        # Visit all adjacent vertices
        for neighbor in self.graph[v]:
            if not visited[neighbor]:
                self._dfs_topological(neighbor, visited, stack)
        
        # Add current vertex to stack after visiting all neighbors
        stack.append(v)

# Example usage
g = Graph(6)
g.add_edge(5, 2)  # Course 5 → Course 2
g.add_edge(5, 0)  # Course 5 → Course 0
g.add_edge(4, 0)  # Course 4 → Course 0
g.add_edge(4, 1)  # Course 4 → Course 1
g.add_edge(2, 3)  # Course 2 → Course 3
g.add_edge(3, 1)  # Course 3 → Course 1

print("Topological Sort (DFS):", g.topological_sort_dfs())
# Output: [5, 4, 2, 3, 1, 0] or [4, 5, 2, 3, 1, 0]
```

### **DFS Example Walkthrough**

**Graph:**
```
5 → 2 → 3 → 1
↓       ↓
0       ↓
↑       ↓
4 ------→
```

**DFS execution starting from vertex 5:**

1. **Visit 5:** Go to neighbors 2 and 0
2. **Visit 2:** Go to neighbor 3
3. **Visit 3:** Go to neighbor 1
4. **Visit 1:** No unvisited neighbors, finish 1, add to stack: [1]
5. **Finish 3:** Add to stack: [1, 3]
6. **Finish 2:** Add to stack: [1, 3, 2]
7. **Visit 0:** No unvisited neighbors, finish 0, add to stack: [1, 3, 2, 0]
8. **Finish 5:** Add to stack: [1, 3, 2, 0, 5]
9. **Visit 4:** Go to neighbors 0 (visited) and 1 (visited)
10. **Finish 4:** Add to stack: [1, 3, 2, 0, 5, 4]

**Result:** Reverse stack = [4, 5, 0, 2, 3, 1]

## **Kahn's Algorithm (BFS-Based)**

### **Algorithm Overview**

**Key insight:** Repeatedly remove vertices with no incoming edges. These vertices can be placed first in topological order.

**Algorithm:**
1. Calculate in-degree for all vertices
2. Add all vertices with in-degree 0 to queue
3. While queue is not empty:
   - Remove vertex from queue, add to result
   - Decrease in-degree of all neighbors
   - Add neighbors with in-degree 0 to queue
4. If result contains all vertices, return it; otherwise, graph has cycle

### **Implementation**

```python
from collections import deque

class Graph:
    def __init__(self, vertices):
        self.V = vertices
        self.graph = defaultdict(list)
    
    def add_edge(self, u, v):
        self.graph[u].append(v)
    
    def topological_sort_kahn(self):
        """Topological sort using Kahn's algorithm"""
        # Calculate in-degrees
        in_degree = [0] * self.V
        for u in range(self.V):
            for v in self.graph[u]:
                in_degree[v] += 1
        
        # Initialize queue with vertices having in-degree 0
        queue = deque()
        for i in range(self.V):
            if in_degree[i] == 0:
                queue.append(i)
        
        result = []
        
        while queue:
            # Remove vertex with no incoming edges
            u = queue.popleft()
            result.append(u)
            
            # Decrease in-degree of all neighbors
            for v in self.graph[u]:
                in_degree[v] -= 1
                if in_degree[v] == 0:
                    queue.append(v)
        
        # Check for cycles
        if len(result) != self.V:
            raise ValueError("Graph contains a cycle")
        
        return result

# Example usage
g = Graph(6)
g.add_edge(5, 2)
g.add_edge(5, 0)
g.add_edge(4, 0)
g.add_edge(4, 1)
g.add_edge(2, 3)
g.add_edge(3, 1)

print("Topological Sort (Kahn):", g.topological_sort_kahn())
# Output: [4, 5, 0, 2, 3, 1] or [5, 4, 0, 2, 3, 1]
```

### **Kahn's Algorithm Walkthrough**

**Initial state:**
```
Vertex: 0  1  2  3  4  5
In-deg: 2  2  1  1  0  0
Queue: [4, 5]
```

**Step 1:** Process vertex 4
- Add 4 to result: [4]
- Decrease in-degree of neighbors 0, 1
- In-degrees: [1, 1, 1, 1, 0, 0]
- Queue: [5]

**Step 2:** Process vertex 5
- Add 5 to result: [4, 5]
- Decrease in-degree of neighbors 2, 0
- In-degrees: [0, 1, 0, 1, 0, 0]
- Queue: [0, 2]

**Step 3:** Process vertex 0
- Add 0 to result: [4, 5, 0]
- No neighbors
- Queue: [2]

**Step 4:** Process vertex 2
- Add 2 to result: [4, 5, 0, 2]
- Decrease in-degree of neighbor 3
- In-degrees: [0, 1, 0, 0, 0, 0]
- Queue: [3]

**Step 5:** Process vertex 3
- Add 3 to result: [4, 5, 0, 2, 3]
- Decrease in-degree of neighbor 1
- In-degrees: [0, 0, 0, 0, 0, 0]
- Queue: [1]

**Step 6:** Process vertex 1
- Add 1 to result: [4, 5, 0, 2, 3, 1]
- Queue: []

**Result:** [4, 5, 0, 2, 3, 1]

## **Cycle Detection**

### **Using DFS (Three Colors)**

```python
def has_cycle_dfs(self):
    """Detect cycle using DFS with three colors"""
    WHITE, GRAY, BLACK = 0, 1, 2
    color = [WHITE] * self.V
    
    def dfs(v):
        if color[v] == GRAY:
            return True  # Back edge found (cycle)
        
        if color[v] == BLACK:
            return False  # Already processed
        
        color[v] = GRAY  # Mark as being processed
        
        for neighbor in self.graph[v]:
            if dfs(neighbor):
                return True
        
        color[v] = BLACK  # Mark as completely processed
        return False
    
    for i in range(self.V):
        if color[i] == WHITE:
            if dfs(i):
                return True
    
    return False
```

### **Using Kahn's Algorithm**

```python
def has_cycle_kahn(self):
    """Detect cycle using Kahn's algorithm"""
    try:
        result = self.topological_sort_kahn()
        return False  # No cycle if topological sort succeeds
    except ValueError:
        return True   # Cycle detected
```

## **Applications of Topological Sorting**

### **Course Scheduling**

```python
class CourseScheduler:
    def __init__(self, num_courses):
        self.num_courses = num_courses
        self.graph = defaultdict(list)
    
    def add_prerequisite(self, course, prerequisite):
        """Add prerequisite relationship"""
        self.graph[prerequisite].append(course)
    
    def can_finish_all_courses(self):
        """Check if all courses can be completed"""
        try:
            schedule = self.get_course_order()
            return len(schedule) == self.num_courses
        except ValueError:
            return False
    
    def get_course_order(self):
        """Get valid course completion order"""
        # Use Kahn's algorithm
        in_degree = [0] * self.num_courses
        
        for u in range(self.num_courses):
            for v in self.graph[u]:
                in_degree[v] += 1
        
        queue = deque([i for i in range(self.num_courses) if in_degree[i] == 0])
        order = []
        
        while queue:
            course = queue.popleft()
            order.append(course)
            
            for next_course in self.graph[course]:
                in_degree[next_course] -= 1
                if in_degree[next_course] == 0:
                    queue.append(next_course)
        
        if len(order) != self.num_courses:
            raise ValueError("Circular dependency detected")
        
        return order

# Example usage
scheduler = CourseScheduler(4)
scheduler.add_prerequisite(1, 0)  # Course 1 requires Course 0
scheduler.add_prerequisite(2, 0)  # Course 2 requires Course 0
scheduler.add_prerequisite(3, 1)  # Course 3 requires Course 1
scheduler.add_prerequisite(3, 2)  # Course 3 requires Course 2

print("Can finish all courses:", scheduler.can_finish_all_courses())
print("Course order:", scheduler.get_course_order())
```

### **Build System Dependencies**

```python
class BuildSystem:
    def __init__(self):
        self.dependencies = defaultdict(list)
        self.targets = set()
    
    def add_dependency(self, target, dependency):
        """Add build dependency"""
        self.dependencies[dependency].append(target)
        self.targets.add(target)
        self.targets.add(dependency)
    
    def get_build_order(self):
        """Get order to build all targets"""
        # Create mapping from target names to indices
        target_list = list(self.targets)
        target_to_index = {target: i for i, target in enumerate(target_list)}
        
        # Build graph using indices
        graph = defaultdict(list)
        for dependency, targets in self.dependencies.items():
            dep_idx = target_to_index[dependency]
            for target in targets:
                target_idx = target_to_index[target]
                graph[dep_idx].append(target_idx)
        
        # Perform topological sort
        in_degree = [0] * len(target_list)
        for u in graph:
            for v in graph[u]:
                in_degree[v] += 1
        
        queue = deque([i for i in range(len(target_list)) if in_degree[i] == 0])
        build_order = []
        
        while queue:
            idx = queue.popleft()
            build_order.append(target_list[idx])
            
            for neighbor_idx in graph[idx]:
                in_degree[neighbor_idx] -= 1
                if in_degree[neighbor_idx] == 0:
                    queue.append(neighbor_idx)
        
        if len(build_order) != len(target_list):
            raise ValueError("Circular dependency in build system")
        
        return build_order

# Example usage
build_system = BuildSystem()
build_system.add_dependency("app.exe", "main.o")
build_system.add_dependency("app.exe", "utils.o")
build_system.add_dependency("main.o", "main.c")
build_system.add_dependency("utils.o", "utils.c")
build_system.add_dependency("main.c", "config.h")
build_system.add_dependency("utils.c", "config.h")

print("Build order:", build_system.get_build_order())
```

### **Task Scheduling**

```python
class TaskScheduler:
    def __init__(self):
        self.tasks = {}
        self.dependencies = defaultdict(list)
    
    def add_task(self, task_id, duration):
        """Add task with duration"""
        self.tasks[task_id] = duration
    
    def add_dependency(self, task, prerequisite):
        """Add task dependency"""
        self.dependencies[prerequisite].append(task)
    
    def schedule_tasks(self):
        """Get task execution schedule with timing"""
        # Get topological order
        task_list = list(self.tasks.keys())
        task_to_index = {task: i for i, task in enumerate(task_list)}
        
        graph = defaultdict(list)
        for prereq, tasks in self.dependencies.items():
            if prereq in task_to_index:
                prereq_idx = task_to_index[prereq]
                for task in tasks:
                    if task in task_to_index:
                        task_idx = task_to_index[task]
                        graph[prereq_idx].append(task_idx)
        
        # Kahn's algorithm
        in_degree = [0] * len(task_list)
        for u in graph:
            for v in graph[u]:
                in_degree[v] += 1
        
        queue = deque([i for i in range(len(task_list)) if in_degree[i] == 0])
        schedule = []
        earliest_start = [0] * len(task_list)
        
        while queue:
            task_idx = queue.popleft()
            task_id = task_list[task_idx]
            
            schedule.append({
                'task': task_id,
                'start_time': earliest_start[task_idx],
                'duration': self.tasks[task_id],
                'end_time': earliest_start[task_idx] + self.tasks[task_id]
            })
            
            # Update earliest start times for dependent tasks
            for dependent_idx in graph[task_idx]:
                earliest_start[dependent_idx] = max(
                    earliest_start[dependent_idx],
                    earliest_start[task_idx] + self.tasks[task_id]
                )
                in_degree[dependent_idx] -= 1
                if in_degree[dependent_idx] == 0:
                    queue.append(dependent_idx)
        
        return schedule

# Example usage
scheduler = TaskScheduler()
scheduler.add_task("A", 3)
scheduler.add_task("B", 2)
scheduler.add_task("C", 4)
scheduler.add_task("D", 1)

scheduler.add_dependency("B", "A")
scheduler.add_dependency("C", "A")
scheduler.add_dependency("D", "B")
scheduler.add_dependency("D", "C")

schedule = scheduler.schedule_tasks()
for task in schedule:
    print(f"Task {task['task']}: Start {task['start_time']}, Duration {task['duration']}, End {task['end_time']}")
```

## **Time and Space Complexity**

### **DFS-Based Approach**

**Time Complexity:** O(V + E)
- Visit each vertex once: O(V)
- Examine each edge once: O(E)
- Total: O(V + E)

**Space Complexity:** O(V)
- Visited array: O(V)
- Recursion stack: O(V) in worst case
- Result stack: O(V)

### **Kahn's Algorithm**

**Time Complexity:** O(V + E)
- Calculate in-degrees: O(V + E)
- Process each vertex once: O(V)
- Examine each edge once: O(E)
- Total: O(V + E)

**Space Complexity:** O(V)
- In-degree array: O(V)
- Queue: O(V) in worst case
- Result array: O(V)

## **Comparison: DFS vs Kahn's Algorithm**

| Aspect | DFS-Based | Kahn's Algorithm |
|--------|-----------|------------------|
| Approach | Recursive | Iterative |
| Cycle Detection | Natural (back edges) | Count processed vertices |
| Memory Usage | O(V) recursion stack | O(V) queue |
| Implementation | Simpler | More intuitive |
| Multiple Solutions | Can find all | Finds one |
| Online Processing | No | Yes (can process as dependencies resolve) |

## **Advanced Applications**

### **Longest Path in DAG**

```python
def longest_path_dag(self, source):
    """Find longest path from source in DAG"""
    # Get topological order
    topo_order = self.topological_sort_dfs()
    
    # Initialize distances
    dist = [-float('inf')] * self.V
    dist[source] = 0
    
    # Process vertices in topological order
    for u in topo_order:
        if dist[u] != -float('inf'):
            for v, weight in self.graph[u]:  # Assuming weighted edges
                dist[v] = max(dist[v], dist[u] + weight)
    
    return dist
```

### **Critical Path Method (CPM)**

```python
def critical_path(self):
    """Find critical path in project network"""
    # Forward pass - earliest start times
    topo_order = self.topological_sort_dfs()
    earliest_start = [0] * self.V
    
    for u in topo_order:
        for v, duration in self.graph[u]:
            earliest_start[v] = max(earliest_start[v], 
                                  earliest_start[u] + duration)
    
    # Backward pass - latest start times
    latest_start = [max(earliest_start)] * self.V
    
    for u in reversed(topo_order):
        for v, duration in self.graph[u]:
            latest_start[u] = min(latest_start[u], 
                                latest_start[v] - duration)
    
    # Find critical activities
    critical_activities = []
    for u in range(self.V):
        if earliest_start[u] == latest_start[u]:
            critical_activities.append(u)
    
    return critical_activities, earliest_start, latest_start
```

## **Common Pitfalls and Tips**

### **1. Forgetting Cycle Detection**
```python
# Wrong - doesn't check for cycles
def naive_topological_sort(self):
    result = self.topological_sort_kahn()
    return result  # May return partial result if cycle exists

# Correct - always check for cycles
def robust_topological_sort(self):
    result = self.topological_sort_kahn()
    if len(result) != self.V:
        raise ValueError("Graph contains cycle - topological sort impossible")
    return result
```

### **2. Incorrect In-degree Calculation**
```python
# Wrong - doesn't handle self-loops or multiple edges
def wrong_in_degree(self):
    in_degree = [0] * self.V
    for u in self.graph:
        for v in self.graph[u]:
            in_degree[v] += 1  # Counts multiple edges multiple times
    return in_degree

# Correct - handle edge cases properly
def correct_in_degree(self):
    in_degree = [0] * self.V
    for u in range(self.V):
        for v in self.graph[u]:
            in_degree[v] += 1
    return in_degree
```

### **3. Not Handling Disconnected Components**
```python
def complete_topological_sort(self):
    """Handle disconnected DAG components"""
    visited = [False] * self.V
    stack = []
    
    # Process all components
    for i in range(self.V):
        if not visited[i]:
            self._dfs_topological(i, visited, stack)
    
    return stack[::-1]
```

## **Practice Problems**

Here are some problems I found helpful during preparation:

1. **Course Schedule** - Basic topological sorting
2. **Course Schedule II** - Return actual ordering
3. **Alien Dictionary** - Topological sort with character ordering
4. **Minimum Height Trees** - Find roots of minimum height trees
5. **Parallel Courses** - Scheduling with time constraints

## **Conclusion**

Topological sorting is a fundamental algorithm for ordering vertices in directed acyclic graphs. Key takeaways:

**Core Concepts:**
- Only works on DAGs (directed acyclic graphs)
- Multiple valid orderings may exist
- Essential for dependency resolution

**Algorithms:**
- DFS-based: Uses finish times, natural recursion
- Kahn's: Uses in-degrees, iterative approach
- Both have O(V + E) time complexity

**Applications:**
- Course scheduling and prerequisites
- Build system dependency resolution
- Task scheduling and project management
- Compiler optimization and instruction scheduling

**For loksewa preparation, focus on:**
- Understanding when topological sorting applies
- Implementing both DFS and Kahn's algorithms
- Detecting cycles in directed graphs
- Recognizing real-world applications
- Analyzing time and space complexity

**Remember:** Topological sorting is about respecting dependencies and prerequisites. The key insight is that in any valid ordering, all dependencies must come before the things that depend on them.

Understanding topological sorting provides a foundation for many advanced graph algorithms and real-world system design problems!
