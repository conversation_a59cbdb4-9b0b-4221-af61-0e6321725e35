---
title: 'Collision Resolution Techniques in Hash Tables - Handling Hash Conflicts'
date: '2025-04-29'
tags: ['DSA']
draft: false
summary: Comprehensive guide to collision resolution techniques in hash tables including chaining, open addressing, linear probing, quadratic probing, and double hashing with examples and analysis.
---

Collision resolution techniques were initially one of the most confusing topics for me in DSA. I kept thinking, "If hash functions are supposed to distribute data evenly, why do we need to handle collisions?" But once I understood that collisions are inevitable due to the pigeonhole principle and that different resolution techniques have vastly different performance characteristics, everything started making sense. Let me share what I've learned about these crucial hashing concepts.

## **Introduction to Hash Collisions**

When I first learned about hash tables, I thought they were magical - O(1) access time seemed too good to be true. And in a way, I was right to be skeptical! Hash collisions are inevitable, and how we handle them determines the actual performance of our hash table.

**Hash collision** occurs when two different keys produce the same hash value. Since hash tables have finite size and we're mapping potentially infinite keys to finite slots, collisions are mathematically unavoidable.

## **Why Do Collisions Occur?**

### **The Pigeonhole Principle**

If you have n pigeonholes and n+1 pigeons, at least one pigeonhole must contain more than one pigeon. Similarly, if we have more keys than hash table slots, collisions are guaranteed.

**Example:**
```
Hash table size: 7
Keys: 10, 22, 31, 4, 15, 28, 17, 88, 59

Hash function: h(key) = key % 7

h(10) = 3
h(22) = 1  
h(31) = 3  ← Collision with 10!
h(4) = 4
h(15) = 1  ← Collision with 22!
```

Even with a good hash function, collisions happen due to:
- Limited table size
- Non-uniform key distribution
- Hash function limitations
- Birthday paradox effect

## **Major Collision Resolution Techniques**

### **1. Separate Chaining (Closed Addressing)**

**Concept:** Each hash table slot contains a pointer to a linked list (or other data structure) that stores all elements hashing to that slot.

**Implementation:**
```python
class HashTableChaining:
    def __init__(self, size):
        self.size = size
        self.table = [[] for _ in range(size)]
    
    def hash_function(self, key):
        return hash(key) % self.size
    
    def insert(self, key, value):
        index = self.hash_function(key)
        bucket = self.table[index]
        
        # Check if key already exists
        for i, (k, v) in enumerate(bucket):
            if k == key:
                bucket[i] = (key, value)  # Update existing
                return
        
        # Add new key-value pair
        bucket.append((key, value))
    
    def search(self, key):
        index = self.hash_function(key)
        bucket = self.table[index]
        
        for k, v in bucket:
            if k == key:
                return v
        
        raise KeyError(f"Key {key} not found")
    
    def delete(self, key):
        index = self.hash_function(key)
        bucket = self.table[index]
        
        for i, (k, v) in enumerate(bucket):
            if k == key:
                del bucket[i]
                return
        
        raise KeyError(f"Key {key} not found")
```

**Example Walkthrough:**
```
Table size: 5
Insert: (10, "A"), (22, "B"), (31, "C"), (4, "D"), (15, "E")

h(10) = 0: [10:"A"]
h(22) = 2: [22:"B"] 
h(31) = 1: [31:"C"]
h(4) = 4:  [4:"D"]
h(15) = 0: [10:"A"] → [15:"E"]  // Chain at index 0

Final table:
[0]: [(10,"A"), (15,"E")]
[1]: [(31,"C")]
[2]: [(22,"B")]
[3]: []
[4]: [(4,"D")]
```

**Advantages:**
- Simple implementation
- No clustering issues
- Table never "fills up"
- Good performance with good hash function

**Disadvantages:**
- Extra memory for pointers
- Cache performance issues
- Worst case O(n) if all keys hash to same slot

**Time Complexity:**
- Average case: O(1) for all operations
- Worst case: O(n) for all operations
- Space complexity: O(n)

### **2. Open Addressing (Closed Hashing)**

**Concept:** All elements are stored directly in the hash table. When collision occurs, we probe for the next available slot using a systematic method.

#### **2.1 Linear Probing**

**Concept:** If slot h(key) is occupied, try h(key)+1, h(key)+2, etc., until an empty slot is found.

**Probe sequence:** h(key), h(key)+1, h(key)+2, ..., (h(key)+i) mod m

**Implementation:**
```python
class HashTableLinearProbing:
    def __init__(self, size):
        self.size = size
        self.keys = [None] * size
        self.values = [None] * size
        self.deleted = [False] * size
    
    def hash_function(self, key):
        return hash(key) % self.size
    
    def insert(self, key, value):
        index = self.hash_function(key)
        
        while self.keys[index] is not None and not self.deleted[index]:
            if self.keys[index] == key:
                self.values[index] = value  # Update existing
                return
            index = (index + 1) % self.size
        
        self.keys[index] = key
        self.values[index] = value
        self.deleted[index] = False
    
    def search(self, key):
        index = self.hash_function(key)
        
        while self.keys[index] is not None:
            if self.keys[index] == key and not self.deleted[index]:
                return self.values[index]
            index = (index + 1) % self.size
        
        raise KeyError(f"Key {key} not found")
    
    def delete(self, key):
        index = self.hash_function(key)
        
        while self.keys[index] is not None:
            if self.keys[index] == key and not self.deleted[index]:
                self.deleted[index] = True
                return
            index = (index + 1) % self.size
        
        raise KeyError(f"Key {key} not found")
```

**Example:**
```
Table size: 7
Insert: 10, 22, 31, 4, 15, 28, 17

h(10) = 3: [_, _, _, 10, _, _, _]
h(22) = 1: [_, 22, _, 10, _, _, _]
h(31) = 3: Collision! Try 4: [_, 22, _, 10, 31, _, _]
h(4) = 4:  Collision! Try 5: [_, 22, _, 10, 31, 4, _]
h(15) = 1: Collision! Try 2: [_, 22, 15, 10, 31, 4, _]
h(28) = 0: [28, 22, 15, 10, 31, 4, _]
h(17) = 3: Collision! Try 4,5,6: [28, 22, 15, 10, 31, 4, 17]
```

**Primary Clustering:** Linear probing suffers from primary clustering where consecutive occupied slots form clusters, increasing search time.

I remember being frustrated by clustering until I understood it's like traffic jams - once they start, they tend to grow!

#### **2.2 Quadratic Probing**

**Concept:** Instead of linear increments, use quadratic increments to reduce clustering.

**Probe sequence:** h(key), h(key)+1², h(key)+2², h(key)+3², ...

**Implementation:**
```python
def quadratic_probe_insert(self, key, value):
    index = self.hash_function(key)
    i = 0
    
    while self.keys[index] is not None and not self.deleted[index]:
        if self.keys[index] == key:
            self.values[index] = value
            return
        i += 1
        index = (self.hash_function(key) + i*i) % self.size
    
    self.keys[index] = key
    self.values[index] = value
    self.deleted[index] = False
```

**Advantages over Linear Probing:**
- Reduces primary clustering
- Better distribution of probes
- Improved average performance

**Disadvantages:**
- Secondary clustering (keys with same hash value follow same probe sequence)
- May not probe all slots
- More complex implementation

#### **2.3 Double Hashing**

**Concept:** Use a second hash function to determine the probe increment.

**Probe sequence:** h₁(key), h₁(key)+h₂(key), h₁(key)+2×h₂(key), ...

**Implementation:**
```python
class HashTableDoubleHashing:
    def __init__(self, size):
        self.size = size
        self.keys = [None] * size
        self.values = [None] * size
    
    def hash1(self, key):
        return hash(key) % self.size
    
    def hash2(self, key):
        # Second hash function (should be relatively prime to table size)
        return 7 - (hash(key) % 7)
    
    def insert(self, key, value):
        index = self.hash1(key)
        step = self.hash2(key)
        
        while self.keys[index] is not None:
            if self.keys[index] == key:
                self.values[index] = value
                return
            index = (index + step) % self.size
        
        self.keys[index] = key
        self.values[index] = value
```

**Advantages:**
- Eliminates both primary and secondary clustering
- Better distribution than linear and quadratic probing
- Can probe all slots if hash functions are chosen properly

**Disadvantages:**
- More complex implementation
- Requires careful selection of second hash function
- Slightly more computation per probe

## **Performance Analysis**

### **Load Factor Impact**

**Load factor (α) = n/m** where n = number of elements, m = table size

| Load Factor | Linear Probing | Quadratic Probing | Double Hashing | Chaining |
|-------------|----------------|-------------------|----------------|----------|
| α = 0.5 | 1.5 probes | 1.4 probes | 1.4 probes | 1.25 probes |
| α = 0.75 | 2.5 probes | 2.0 probes | 1.8 probes | 1.37 probes |
| α = 0.9 | 5.5 probes | 3.2 probes | 2.6 probes | 1.45 probes |

### **Time Complexity Summary**

| Technique | Average Insert | Average Search | Average Delete | Worst Case |
|-----------|----------------|----------------|----------------|------------|
| Chaining | O(1) | O(1) | O(1) | O(n) |
| Linear Probing | O(1) | O(1) | O(1) | O(n) |
| Quadratic Probing | O(1) | O(1) | O(1) | O(n) |
| Double Hashing | O(1) | O(1) | O(1) | O(n) |

## **Choosing the Right Technique**

### **Use Chaining When:**
- Memory is not a primary concern
- Load factor might exceed 0.75
- Simple implementation is preferred
- Deletions are frequent

### **Use Linear Probing When:**
- Memory efficiency is important
- Cache performance matters
- Load factor is kept low (< 0.5)
- Simple implementation needed

### **Use Quadratic Probing When:**
- Want to avoid primary clustering
- Moderate load factors (0.5-0.75)
- Better than linear but simpler than double hashing

### **Use Double Hashing When:**
- Best performance is required
- Load factors might be high
- Willing to accept implementation complexity
- Uniform distribution is critical

## **Advanced Techniques**

### **Robin Hood Hashing**
- Variant of open addressing
- Minimizes variance in probe distances
- "Rich" elements give way to "poor" ones

### **Cuckoo Hashing**
- Guarantees O(1) worst-case lookup
- Uses two hash functions and tables
- May require rehashing on insertion

### **Consistent Hashing**
- Used in distributed systems
- Minimizes rehashing when table size changes
- Important for load balancing

## **Practical Considerations**

### **Hash Function Quality**
```python
# Good hash function characteristics:
def good_hash(key):
    # 1. Deterministic
    # 2. Uniform distribution
    # 3. Fast computation
    # 4. Avalanche effect
    return hash(key)  # Python's built-in is generally good
```

### **Resizing Strategy**
```python
def resize_table(self):
    old_keys = self.keys
    old_values = self.values
    
    self.size *= 2  # Double the size
    self.keys = [None] * self.size
    self.values = [None] * self.size
    
    # Rehash all elements
    for i in range(len(old_keys)):
        if old_keys[i] is not None:
            self.insert(old_keys[i], old_values[i])
```

### **Deletion Handling**
- **Chaining:** Simple removal from list
- **Open Addressing:** Use tombstone markers to maintain probe sequences

## **Common Pitfalls**

1. **Ignoring load factor:** Performance degrades rapidly with high load factors
2. **Poor hash function:** Can cause excessive clustering
3. **Incorrect deletion:** In open addressing, simple deletion breaks probe sequences
4. **Not resizing:** Table becomes inefficient as load factor increases

## **Real-world Applications**

### **Database Indexing**
- Hash indexes for equality lookups
- Collision resolution affects query performance

### **Caching Systems**
- Web caches use hash tables
- Collision handling affects cache hit rates

### **Compiler Symbol Tables**
- Variable name lookup
- Function resolution

## **Conclusion**

Understanding collision resolution techniques is crucial for implementing efficient hash tables. Key takeaways:

**Chaining:**
- Simple and robust
- Good for high load factors
- Extra memory overhead

**Open Addressing:**
- Memory efficient
- Performance sensitive to load factor
- Various probing strategies available

**For loksewa preparation, focus on:**
- Understanding why collisions occur
- Implementing basic chaining and linear probing
- Analyzing time complexity under different load factors
- Recognizing when to use each technique
- Understanding the trade-offs between memory and performance

Remember, the choice of collision resolution technique can dramatically affect your hash table's performance, so understanding these trade-offs is essential for both exams and real-world programming!
